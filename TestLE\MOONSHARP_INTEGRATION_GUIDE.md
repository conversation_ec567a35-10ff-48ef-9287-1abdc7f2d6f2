# MoonSharp Integration Guide for TestLE

## Overview

This guide provides comprehensive information about the MoonSharp Lua scripting integration that has been successfully added to your TestLE mod. MoonSharp allows you to write custom game logic in Lua scripts that can interact with the game through a robust C# API.

## What Was Implemented

### 1. Core Integration Files

- **`LuaScriptManager.cs`** - Main manager class that handles script loading, execution, and API registration
- **`LuaAPI.cs`** - Contains all the C# functions that can be called from Lua scripts
- **`Scripts/`** directory - Contains Lua scripts and documentation

### 2. Integration with Main Mod

The LuaScriptManager has been integrated into your main `Test.cs` class:
- Initializes automatically when the mod starts
- Provides hotkeys for script management (F9 to reload, F10 to test)
- Cleans up resources when the application exits

### 3. Comprehensive API

The integration provides access to:
- **Player functions**: Health, position, movement
- **Combat functions**: Ability usage, potion consumption
- **Utility functions**: Enemy detection, distance calculations
- **Game state functions**: Scene information, combat status
- **Advanced functions**: Nearby enemies/items enumeration

## Quick Start

### 1. Basic Usage

Scripts are automatically loaded from the `Scripts/` directory. The main entry point is `init.lua`.

### 2. Hotkeys

- **F9**: Reload all Lua scripts
- **F10**: Execute `basicCombatRoutine()` function

### 3. Example Script

```lua
-- Basic combat routine
function basicCombatRoutine()
    if not isPlayerValid() then
        return false
    end
    
    -- Check health and use potion if needed
    if getPlayerHealthPercent() < 0.3 then
        usePotion()
        return true
    end
    
    -- Find and attack enemies
    local enemy = findNearestEnemy(30.0)
    if enemy ~= nil then
        local playerPos = getPlayerPosition()
        local distance = getDistance(playerPos, enemy.position)
        
        if distance > 5.0 then
            moveToVector(enemy.position)
        else
            useAbility(0)  -- Basic attack
        end
        return true
    end
    
    return false
end
```

## Available Scripts

### 1. Example Scripts

- **`init.lua`** - Initialization script with basic examples
- **`advanced_example.lua`** - Comprehensive example showing all features
- **`test_integration.lua`** - Test suite to verify functionality
- **`combat.lua`** - Basic combat routine example

### 2. Documentation

- **`README.md`** - Complete API reference and usage guide
- **`MOONSHARP_INTEGRATION_GUIDE.md`** - This guide

## API Reference Summary

### Logging Functions
```lua
log(message)           -- Log an info message
logError(message)      -- Log an error message  
logWarning(message)    -- Log a warning message
```

### Player Functions
```lua
getPlayer()                -- Returns player object (or nil)
getPlayerPosition()        -- Returns Vector3 of player position
getPlayerHealth()          -- Returns current health (float)
getPlayerMaxHealth()       -- Returns maximum health (float)
getPlayerHealthPercent()   -- Returns health as percentage (0.0-1.0)
```

### Movement Functions
```lua
moveTo(x, y, z)           -- Move to specific coordinates
moveToVector(vector3)     -- Move to Vector3 position
```

### Combat Functions
```lua
useAbility(index)                    -- Use ability by index (0-4)
useAbilityAt(index, x, y, z)        -- Use ability at specific location
usePotion()                         -- Use health potion
```

### Utility Functions
```lua
findNearestEnemy(maxDistance)       -- Find nearest enemy within range
getDistance(pos1, pos2)             -- Calculate distance between positions
createVector3(x, y, z)              -- Create a Vector3 object
```

### Game State Functions
```lua
getCurrentScene()                   -- Get current scene name
isInCombat()                       -- Check if player is in combat
```

## Testing the Integration

### 1. Verify Installation

Run the test suite to ensure everything is working:

```lua
-- Load and run the test suite
dofile("test_integration.lua")
runAllTests()
```

### 2. Basic Test

```lua
-- Quick functionality test
quickTest()
```

### 3. Manual Testing

```lua
-- Test individual functions
printPlayerInfo()
testMovement()
testAbilities()
```

## Project Structure

```
TestLE/
├── LuaScriptManager.cs          -- Main Lua integration manager
├── LuaAPI.cs                    -- C# API functions for Lua
├── Test.cs                      -- Updated with Lua integration
├── Scripts/                     -- Lua scripts directory
│   ├── README.md               -- Complete API documentation
│   ├── init.lua                -- Initialization script
│   ├── advanced_example.lua    -- Comprehensive example
│   ├── test_integration.lua    -- Test suite
│   └── combat.lua              -- Basic combat example
└── MOONSHARP_INTEGRATION_GUIDE.md -- This guide
```

## Best Practices

### 1. Error Handling

Always wrap your code in proper error handling:

```lua
function safeFunction()
    local success, result = pcall(function()
        -- Your code here
        return someOperation()
    end)
    
    if not success then
        logError("Error in safeFunction: " .. tostring(result))
        return false
    end
    
    return result
end
```

### 2. Performance Considerations

- Use throttling for frequently called functions
- Cache expensive calculations
- Avoid creating objects in tight loops

### 3. State Management

Use global tables to maintain state between function calls:

```lua
local state = {
    inCombat = false,
    lastTarget = nil,
    combatStartTime = 0
}
```

## Troubleshooting

### Common Issues

1. **Script not loading**: Check file syntax with a Lua validator
2. **Functions not found**: Ensure the function name is spelled correctly
3. **Player functions returning nil**: Player may not be initialized yet
4. **Performance issues**: Add throttling to frequently called functions

### Debugging

Use the test script to verify functionality:

```lua
runAllTests()
```

Enable debug logging in your scripts:

```lua
local DEBUG = true

function debugLog(message)
    if DEBUG then
        log("[DEBUG] " .. message)
    end
end
```

## Next Steps

### 1. Create Custom Scripts

Start by modifying the example scripts to suit your needs:
- Customize combat routines for your character class
- Add automation for specific game activities
- Create utility scripts for common tasks

### 2. Advanced Features

Explore advanced functionality:
- Scene-specific behaviors
- Complex state management
- Integration with existing mod features

### 3. Extend the API

If you need additional functionality:
- Add new C# functions to `LuaAPI.cs`
- Register them in `LuaScriptManager.cs`
- Update the documentation

## Package Information

- **MoonSharp Version**: *******
- **Target Framework**: .NET 6.0
- **Integration Status**: ✅ Successfully integrated and tested

## Support

For issues or questions:

1. Check the test script output for errors
2. Review the mod logs for Lua-related messages
3. Verify your Lua syntax using online validators
4. Test with the provided examples first
5. Refer to the comprehensive API documentation in `Scripts/README.md`

The MoonSharp integration is now fully functional and ready for use. You can start writing Lua scripts to extend your mod's functionality immediately!
