﻿using Il2Cpp;
using TestLE.Routines;
using TestLE.Types;
using TestLE.Utilities;
using UnityEngine;
using UnityEngine.UI;

namespace TestLE;

public static class Globals
{
    public static LocalPlayer PLAYER => LocalPlayer.instance;
    public static MainRoutine? MAIN_ROUTINE { get; set; }
    public static object? MAIN_ROUTINE_COROUTINE { get; set; }
    public static string? CURRENT_SCENE { get; set; }
    public static CombatRoutine? CURRENT_ROUTINE { get; set; }
    public static List<MonolithObjective?> MONOLITH_OBJECTIVES { get; } = new();
    public static List<WorldObjectClickListener?> GOOD_SHRINES { get; } = new();
    public static List<Enemy?> ENEMIES { get; } = new();
    public static List<WorldObjectClickListener?> INTERACTABLES { get; } = new();
    public static List<GroundItem?> GROUND_ITEMS { get; } = new();
    public static DateTime LAST_GROUND_ITEM_DROP { get; set; } = DateTime.MinValue;
    public static bool SHOW_UI { get; set; } = true;

    private static Camera _CAMERA = null!;

    public static Camera CAMERA
    {
        get
        {
            if (_CAMERA == null)
                _CAMERA = Camera.main!;

            return _CAMERA;
        }
    }

    private static Button? _STORE_MATERIALS_BUTTON;

    public static Button? STORE_MATERIALS_BUTTON
    {
        get
        {
            if (_STORE_MATERIALS_BUTTON == null)
                _STORE_MATERIALS_BUTTON = FindHelpers.FindStoreMaterialsButton();

            return _STORE_MATERIALS_BUTTON;
        }
    }


    public static AuctionHouseUI AUCTION_HOUSE_UI { get; set; } = new();
    public static AuctionHouse? AUCTION_HOUSE { get; set; }
    
    
    public static void ResetGlobals()
    {
        MONOLITH_OBJECTIVES.Clear();
        GOOD_SHRINES.Clear();
        ENEMIES.Clear();
        INTERACTABLES.Clear();
        GROUND_ITEMS.Clear();
        LAST_GROUND_ITEM_DROP = DateTime.MinValue;
    }
}
