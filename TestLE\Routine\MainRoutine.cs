﻿using System.Collections;
using Il2Cpp;
using MelonLoader;
using TestLE.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;
using Random = UnityEngine.Random;

namespace TestLE.Routines;

public class MainRoutine : IEnumerator
{
    public object? Current { get; private set; }

    private DateTime _lastStoreMaterialsTime { get; set; } = DateTime.MinValue;
    private float _idleTime { get; set; }
    private Dictionary<Enemy, int> _enemyInactiveFailSafe { get; } = new();
    private int _currentStashTab { get; set; }


    public bool MoveNext()
    {
        if (this != MAIN_ROUTINE)
        {
            MelonLogger.Msg("MainRoutine is not the current routine!");
            return false;
        }

        if (PLAYER == null)
        {
            MelonLogger.Msg("Player is null!");
            return false;
        }

        if (CURRENT_ROUTINE == null)
        {
            MelonLogger.Msg("Current routine is null!");
            return false;
        }

        var deathScreen = FindHelpers.FindDeathScreen();
        if (deathScreen != null && deathScreen.isActiveAndEnabled)
        {
            MelonLogger.Msg("Death screen found!");
            Current = DeathAndRespawn(deathScreen);
            return true;
        }

        // Store materials every 10 seconds
        if (_lastStoreMaterialsTime == DateTime.MinValue || (DateTime.Now - _lastStoreMaterialsTime).TotalSeconds >= 10)
        {
            PlayerHelpers.StoreMaterials();
            _lastStoreMaterialsTime = DateTime.Now;
        }

        // Move to random position if idle for 5 seconds
        if (PLAYER.movingState.myAgent.velocity.magnitude <= 0.1f)
        {
            _idleTime += Time.deltaTime;
            if (_idleTime >= 5f)
            {
                _idleTime = 0;
                if (UnityHelpers.RandomPointOnNavMesh(PLAYER.transform.position, 10f, out var movePos))
                    PlayerHelpers.MoveTo(movePos);
                else
                    PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-50, 50), 0, Random.Range(-50, 50)));

                Current = Wait(2f);
                return true;
            }
        }
        else
        {
            _idleTime = 0;
        }

        // Find monolith completed button
        var button = FindHelpers.FindMonolithCompleteButton();
        if (button != null)
        {
            Current = MonolithCompleteRoutine();
            return true;
        }

        // Find nearest enemy
        var (enemy, distance) = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 100);
        if (enemy == null)
        {
            MelonLogger.Msg("No mobs found!");
            PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-5, 5), 0, Random.Range(-5, 5)));
            Current = Wait(1f);
            return true;
        }

        // var distance = Vector3.Distance(PLAYER.transform.position, enemy.Data.transform.position);

        // Check if we are in range of loot, if so, loot
        if (distance > 3f && GROUND_ITEMS.Count > 0)
        {
            Current = HandleLoot();
            return true;
        }

        if (distance <= 3)
        {
            // MelonLogger.Msg("Doing combat routine!");
            Current = CombatRoutine(enemy, distance);
            return true;
        }

        // Move to interactable and click it if it is within range
        for (var i = 0; i < INTERACTABLES.Count; i++)
        {
            var interactable = INTERACTABLES[i];
            if (interactable == null || !interactable.isActiveAndEnabled)
            {
                INTERACTABLES.RemoveAt(i);
                i--;
                continue;
            }

            var interactablePos = interactable.transform.position;
            if (Vector3.Distance(PLAYER.transform.position, interactablePos) > 20f)
                continue;

            Current = HandleInteractable(interactable);
            return true;
        }

        // Check if we are in combat range, if so, do combat routine
        if (distance <= CURRENT_ROUTINE.CombatDistance)
        {
            // MelonLogger.Msg("Doing combat routine!");
            Current = CombatRoutine(enemy, distance);
            return true;
        }

        // Do good shrines before map objective
        if (GOOD_SHRINES.Count > 0)
        {
            Current = HandleGoodShrines();
            return true;
        }

        if (MONOLITH_OBJECTIVES.Count == 0)
        {
            // MelonLogger.Msg("Objective pulses not found, defaulting to combat routine as objective is most likely seal gate or ambush etc.");
            Current = CombatRoutine(enemy, distance);
            return true;
        }

        var objective = MONOLITH_OBJECTIVES.FirstOrDefault();
        if (objective == null)
        {
            // MelonLogger.Msg("Objective is null!");
            Current = CombatRoutine(enemy, distance);
            MONOLITH_OBJECTIVES.RemoveAt(0);
            return true;
        }

        var enemyObjective = objective.GetEnemyObjective();
        if (enemyObjective != null)
        {
            // MelonLogger.Msg("Objective is enemy!");
            Current = MoveToMonolithObjective_Enemy(enemyObjective);
            return true;
        }

        var clickObjective = objective.GetClickObjective();
        if (clickObjective != null)
        {
            // MelonLogger.Msg("Objective is clickable!");
            Current = MoveToMonolithObjective_Click(clickObjective);
            return true;
        }

        // MelonLogger.Msg("Objective not valid, defaulting to combat routine.");
        Current = CombatRoutine(enemy, distance);
        return true;
    }

    public void Reset()
    {
        Current = null;
    }

    private IEnumerator CombatRoutine(Enemy enemy, float distance)
    {
        // if (distance <= 1f && !enemy.Data.isActiveAndEnabled)
        if (distance <= 3f && enemy.Data.Data.actorName != "Exiled Mage" && (!enemy.Data.actorSync.gameObject.active || !enemy.Data.isActiveAndEnabled))
        {
            MelonLogger.Msg("Enemy is too close and not active!");
            enemy.RemoveEnemy();
            yield break;
        }

        var enemyTransform = enemy.Data.transform;
        if (enemy.Data.gameObject.active)
        {
            yield return CURRENT_ROUTINE!.Run(enemy, enemyTransform, distance);
        }
        else
        {
            if (distance <= 3f)
            {
                _enemyInactiveFailSafe[enemy] = _enemyInactiveFailSafe.GetValueOrDefault(enemy) + 1;
                if (_enemyInactiveFailSafe[enemy] >= 10)
                {
                    MelonLogger.Msg("Enemy is inactive for 10 tries, removing enemy!");
                    enemy.RemoveEnemy();
                    _enemyInactiveFailSafe.Remove(enemy);
                }
            }

            PlayerHelpers.MoveTo(enemyTransform.position);
            yield return new WaitForSeconds(0.3333f);
        }
    }

    private IEnumerator MonolithCompleteRoutine()
    {
        // Stop player by moving to the current position
        PlayerHelpers.MoveTo(PLAYER.transform.position);
        yield return new WaitForSeconds(1f);

        // Make portal
        PlayerHelpers.UsePortal();
        yield return new WaitForSeconds(2f);

        // Find portal
        var portal = FindHelpers.FindMonolithPortal();
        if (portal == null)
        {
            MelonLogger.Msg("Portal not found!");
            yield break;
        }

        // Wait for all ground items to be spawned
        if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
            yield return new WaitForSeconds(1f);

        // Loot all ground items before moving to portal
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Move to portal
        yield return PlayerHelpers.MoveToForce(portal.transform.position);

        // Click on portal
        portal.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        // Clear enemies
        // ENEMIES.Clear();
        // MONOLITH_OBJECTIVES.Clear();
        // GROUND_ITEMS.Clear();
        ResetGlobals();
        _enemyInactiveFailSafe.Clear();

        // Click on reward chest
        var chest = FindHelpers.FindMonolithCompleteRewardChest();
        if (chest.obj != null && chest.isActive)
        {
            yield return PlayerHelpers.MoveToForce(chest.obj.transform.position);

            chest.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Chest not found!");
        }

        // Loot all ground items
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Click on reward rock
        var rock = FindHelpers.FindMonolithCompleteRewardRock();
        if (rock.obj != null && rock.isActive)
        {
            yield return PlayerHelpers.MoveToForce(rock.obj.transform.position);

            rock.obj.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }
        else
        {
            MelonLogger.Msg("Rock not found!");
        }

        // Wait for 1 second, to allow the loot to finish spawning
        yield return new WaitForSeconds(1f);

        // Move and collect XP tomes
        var tomes = FindHelpers.FindGroundXPTomes();
        foreach (var t in tomes)
        {
            yield return PlayerHelpers.MoveToForce(t.transform.position);
        }

        // Loot all ground items
        while (GROUND_ITEMS.Count > 0)
        {
            yield return HandleLoot();
            yield return new WaitForSeconds(0.1f);
        }

        // Store all materials
        PlayerHelpers.StoreMaterials();

        // Find stash opener, and move to it, then stash all items
        yield return StashItems();

        // Move to next monolith
        yield return GoNextMonolith();
        MelonLogger.Msg("Monolith completed!");
    }

    private static IEnumerator GoNextMonolith()
    {
        // Find monolith stone
        var stone = FindHelpers.FindMonolithStone();
        if (stone == null)
        {
            MelonLogger.Msg("Stone not found!");
            yield break;
        }

        // Move to monolith stone
        yield return PlayerHelpers.MoveToForce(stone.transform.position);

        // Click on monolith stone
        stone.ObjectClick(PLAYER.gameObject, true);
        yield return new WaitForSeconds(1f);

        // Find monolith islands
        var islands = FindHelpers.FindMonolithIslands();
        if (islands.Count == 0)
        {
            MelonLogger.Msg("Islands not found!");
            yield break;
        }

        // Click on next island
        foreach (var (_, ui) in islands)
        {
            if (ui.island.completed)
                continue;

            if (ui.island.islandType is not EchoWebIsland.IslandType.Normal and not EchoWebIsland.IslandType.Arena and not EchoWebIsland.IslandType.Beacon)
                continue;

            var hasConnectionWithComepletedIsland = false;
            foreach (var c in ui.island.connectedHexes)
            {
                var connectedIsland = islands.GetValueOrDefault(c);
                if (connectedIsland == null)
                    continue;

                if (!connectedIsland.island.completed)
                    continue;

                hasConnectionWithComepletedIsland = true;
                break;
            }

            if (!hasConnectionWithComepletedIsland)
                continue;

            MelonLogger.Msg($"Next monolith reward: {(ui.rewards.Count > 0 ? ui.rewards.getAtIndexOrFirst(0).rewardType : "NULL")}");
            ui.rightClicked();
            break;
        }

        while (SceneManager.GetActiveScene().name == "M_Rest")
            yield return new WaitForSeconds(1f);

        yield return new WaitForSeconds(1f);
        yield return CURRENT_ROUTINE!.OnNewArea();
    }

    private static IEnumerator DeathAndRespawn(DeathScreen deathScreen)
    {
        deathScreen.NormalRespawnClick();
        yield return new WaitForSeconds(1f);

        yield return GoNextMonolith();
    }

    private static IEnumerator HandleLoot()
    {
        MelonLogger.Msg("Handling loot!");
        var groundItem = GROUND_ITEMS.FirstOrDefault();
        if (groundItem == null)
        {
            MelonLogger.Msg("Ground item is null!");
            if (GROUND_ITEMS.Count > 0)
                GROUND_ITEMS.RemoveAt(0);

            yield break;
        }

        MelonLogger.Msg("Moving to ground item!");
        yield return groundItem.MoveToItem();
        groundItem.Pickup();
    }

    private static IEnumerator HandleInteractable(WorldObjectClickListener interactable)
    {
        yield return PlayerHelpers.MoveToForce(interactable.transform.position, interactable.interactionRange * 0.8f);
        // yield return PlayerHelpers.MoveToForce(MathHelpers.GetPositionBetweenTargetAndPlayer(interactable.transform.position, interactable.interactionRange));

        INTERACTABLES.Remove(interactable);
        interactable.ObjectClick(PLAYER.gameObject, true);
    }

    private static IEnumerator HandleGoodShrines()
    {
        for (var i = 0; i < GOOD_SHRINES.Count; i++)
        {
            var s = GOOD_SHRINES[i];
            if (s == null)
            {
                GOOD_SHRINES.RemoveAt(i);
                i--;
            }
        }

        var playerPos = PLAYER.transform.position;
        GOOD_SHRINES.Sort((v1, v2) => Vector3.Distance(playerPos, v1!.transform.position).CompareTo(Vector3.Distance(playerPos, v2!.transform.position)));

        var shrine = GOOD_SHRINES.First();
        PlayerHelpers.MoveTo(shrine!.transform.position);
        yield return new WaitForSeconds(0.3333f);

        // ReSharper disable once Unity.InefficientPropertyAccess
        var distance = Vector3.Distance(PLAYER.transform.position, shrine.transform.position);
        if (distance <= shrine.interactionRange)
        {
            shrine.ObjectClick(PLAYER.gameObject, true);
            GOOD_SHRINES.Remove(shrine);
        }
    }

    private static IEnumerator MoveToMonolithObjective_Enemy(ActorVisuals enemyObjective)
    {
        PlayerHelpers.MoveTo(enemyObjective.transform.position);
        yield return new WaitForSeconds(0.3333f);
    }

    private static IEnumerator MoveToMonolithObjective_Click(WorldObjectClickListener clickObjective)
    {
        var objectiveTransform = clickObjective.transform;
        var objectivePosition = objectiveTransform.position;

        // var directionToPlayer = (PLAYER!.transform.position - objectivePosition).normalized;
        // var targetPosition = objectivePosition - directionToPlayer * (clickObjective.interactionRange * 0.8f);

        // PlayerHelpers.MoveTo(targetPosition);
        PlayerHelpers.MoveTo(objectivePosition);
        yield return new WaitForSeconds(0.3333f);

        // Disable the warning because we waited 0.3333 seconds, which means we can't use the cached position of the objective, only the transform component
        // ReSharper disable once Unity.InefficientPropertyAccess
        var distance = Vector3.Distance(PLAYER.transform.position, objectiveTransform.position);
        if (distance <= clickObjective.interactionRange)
            clickObjective.ObjectClick(PLAYER.gameObject, true);
    }

    private static IEnumerator Wait(float duration)
    {
        yield return new WaitForSeconds(duration);
    }

    private IEnumerator StashItems()
    {
        // Find stash opener, and move to it
        var stashOpener = FindHelpers.FindStashOpener();
        if (stashOpener == null)
        {
            MelonLogger.Msg("StashOpener not found!");
            yield break;
        }

        yield return PlayerHelpers.MoveToForce(stashOpener.transform.position);
        stashOpener.OnUse();

        yield return new WaitForSeconds(0.1f);

        // Find inventory UI, and move all items to stash
        var inventoryUI = FindHelpers.FindInventoryUI();
        if (inventoryUI == null)
        {
            MelonLogger.Msg("InventoryUI not found!");
            yield break;
        }

        if (inventoryUI.container == null)
        {
            MelonLogger.Msg("inventoryUI.container not found!");
            yield break;
        }

        var content = inventoryUI.container.GetContent();
        if (content == null)
        {
            MelonLogger.Msg("inventoryUI.container.GetContent() is null!");
            yield break;
        }

        var stashNavigable = FindHelpers.FindStashNavigable();
        if (stashNavigable == null)
        {
            MelonLogger.Msg("StashNavigable not found!");
            yield break;
        }

        var stashTabUI = FindHelpers.FindStashTabUI();
        if (stashTabUI == null)
        {
            MelonLogger.Msg("StashTabUI not found!");
            yield break;
        }

        yield return SelectCurrentStashTab(stashNavigable, stashTabUI);

        while (content.Count > 0)
        {
            var items = new List<ItemContainerEntry>();
            foreach (var i in content)
            {
                if (i == null)
                {
                    MelonLogger.Msg("Item is null!");
                    continue;
                }

                items.Add(i);
            }

            foreach (var i in items)
                inventoryUI.TryQuickMove(i.Position);

            yield return new WaitForSeconds(0.1f);

            if (content.Count == 0)
                break;

            MelonLogger.Msg("Stashing items failed, trying next stash tab!");
            yield return SelectNextStashTab(stashNavigable, stashTabUI);
        }

        yield return new WaitForSeconds(0.1f);
    }

    private IEnumerator SelectCurrentStashTab(StashTabsNavigable stashNavigable, StashTabbedUIControls stashTabUI) // TODO: Set the stash tab index 0 first
    {
        stashTabUI.SwitchToTab(0);
        stashNavigable.ResetIndex();
        for (var i = 0; i < _currentStashTab; i++)
            // stashTabUI.NextTab();
            stashNavigable.IndexToRight();

        stashNavigable.ClickOnTab();
        yield return new WaitForSeconds(0.1f);
    }

    private IEnumerator SelectNextStashTab(StashTabsNavigable stashNavigable, StashTabbedUIControls stashTabUI)
    {
        _currentStashTab++;
        // stashTabUI.NextTab();
        stashNavigable.IndexToRight();
        stashNavigable.ClickOnTab();
        yield return new WaitForSeconds(0.1f);
    }
}
