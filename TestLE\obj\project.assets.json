{"version": 3, "targets": {"net6.0": {"MoonSharp/2.0.0": {"type": "package", "compile": {"lib/netstandard1.6/MoonSharp.Interpreter.dll": {"related": ".deps.json;.pdb;.xml"}}, "runtime": {"lib/netstandard1.6/MoonSharp.Interpreter.dll": {"related": ".deps.json;.pdb;.xml"}}}}}, "libraries": {"MoonSharp/2.0.0": {"sha512": "uiAcRh7d+53k3xW9pFDJfAFVw4RnjHVCJG05M3oPAVEVwPtFavhg1H/IpC6So4X1j9kJlzuLlA3OghhPcIvc5A==", "type": "package", "path": "moonsharp/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35-client/MoonSharp.Interpreter.dll", "lib/net35-client/MoonSharp.Interpreter.pdb", "lib/net35-client/MoonSharp.Interpreter.xml", "lib/net40-client/MoonSharp.Interpreter.dll", "lib/net40-client/MoonSharp.Interpreter.pdb", "lib/net40-client/MoonSharp.Interpreter.xml", "lib/netcore/MoonSharp.Interpreter.deps.json", "lib/netcore/MoonSharp.Interpreter.dll", "lib/netcore/MoonSharp.Interpreter.pdb", "lib/netcore/MoonSharp.Interpreter.xml", "lib/netstandard1.6/MoonSharp.Interpreter.deps.json", "lib/netstandard1.6/MoonSharp.Interpreter.dll", "lib/netstandard1.6/MoonSharp.Interpreter.pdb", "lib/netstandard1.6/MoonSharp.Interpreter.xml", "lib/portable-net4+sl5+wp8+win8/MoonSharp.Interpreter.dll", "lib/portable-net4+sl5+wp8+win8/MoonSharp.Interpreter.pdb", "lib/portable-net4+sl5+wp8+win8/MoonSharp.Interpreter.xml", "moonsharp.2.0.0.nupkg.sha512", "moonsharp.nuspec"]}}, "projectFileDependencyGroups": {"net6.0": ["MoonSharp >= 2.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\TestLE\\TestLE\\TestLE.csproj", "projectName": "TestLE", "projectPath": "D:\\Projects\\TestLE\\TestLE\\TestLE.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\TestLE\\TestLE\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"MoonSharp": {"target": "Package", "version": "[2.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Users\\<USER>\\.dotnet\\sdk\\10.0.100-preview.7.25380.108\\RuntimeIdentifierGraph.json"}}}}