{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"AutoPatchGenerator/1.0.0": {"dependencies": {"Microsoft.CodeAnalysis.CSharp": "4.3.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.3.0"}, "runtime": {"AutoPatchGenerator.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CodeAnalysis.Common/4.3.0": {"dependencies": {"System.Collections.Immutable": "6.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.300.22.43017"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.3.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.300.22.43017"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.3.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.3.0", "Microsoft.CodeAnalysis.Common": "4.3.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.300.22.43017"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.3.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.3.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.300.22.43017"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Buffers/4.5.1": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/netstandard2.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/netstandard2.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Pipelines/6.0.3": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.Memory/4.5.4": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.Numerics.Vectors/4.4.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Reflection.Metadata/5.0.0": {"dependencies": {"System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "4.2.0.1", "fileVersion": "4.6.28619.1"}}}}}, "libraries": {"AutoPatchGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hhaw6DKZHiR+vgOdIqvndfUntJhmDR7MjylUJ55EvWtDyJFLDf2eij8r9tcwXP35FLD+bVNNCO0+KIYuvJjNnA==", "path": "microsoft.codeanalysis.common/4.3.0", "hashPath": "microsoft.codeanalysis.common.4.3.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-0PU4a2h7L6N9SlF/oNHwj2A/+n0LK/7n6PEGvXyIZq8hc7r/TztB+47mhVLvapT6bWSV7nMT78cNxbQuC6tk6g==", "path": "microsoft.codeanalysis.csharp/4.3.0", "hashPath": "microsoft.codeanalysis.csharp.4.3.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-CH2kezeYUD9/+nGs7QZ6xpOqD6OH6000YkAyU8LbhqLp7W65Xb+HerBzDX9yfprmRwArlF887QhZd8nW6enHIg==", "path": "microsoft.codeanalysis.csharp.workspaces/4.3.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.3.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oFF7lU3dQIeEbAVF6Qq/YEA4jvys3oQKkDezP8NcQTFgD8uRUiIA4NimRxANaWxK2n2PT4DAQspIOtrAE2hSdA==", "path": "microsoft.codeanalysis.workspaces.common/4.3.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Numerics.Vectors/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "path": "system.numerics.vectors/4.4.0", "hashPath": "system.numerics.vectors.4.4.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}}