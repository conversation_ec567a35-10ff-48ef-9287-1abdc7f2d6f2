-- Advanced TestLE Lua Script Example
-- This script demonstrates all available MoonSharp integration features

log("Loading advanced example script...")

-- Global configuration
local config = {
    healthThreshold = 0.3,  -- Use potion when health below 30%
    combatRange = 25.0,     -- Look for enemies within 25 units
    attackRange = 5.0,      -- Attack when enemy is within 5 units
    debugMode = true        -- Enable debug logging
}

-- State tracking
local state = {
    lastHealthCheck = 0,
    lastCombatCheck = 0,
    inCombat = false,
    currentTarget = nil
}

-- Utility functions
function debugLog(message)
    if config.debugMode then
        log("[DEBUG] " .. message)
    end
end

function isPlayerHealthy()
    local healthPercent = getPlayerHealthPercent()
    return healthPercent > config.healthThreshold
end

function checkAndUsePotion()
    if not isPlayerHealthy() then
        local healthPercent = getPlayerHealthPercent()
        log("Health low (" .. tostring(math.floor(healthPercent * 100)) .. "%), using potion")
        usePotion()
        return true
    end
    return false
end

-- Combat system
function findBestTarget()
    local enemy = findNearestEnemy(config.combatRange)
    if enemy ~= nil then
        local playerPos = getPlayerPosition()
        local distance = getDistance(playerPos, enemy.position)
        
        debugLog("Found enemy at distance: " .. tostring(distance))
        return {
            enemy = enemy,
            distance = distance
        }
    end
    return nil
end

function executeAttackSequence(target)
    local distance = target.distance
    
    if distance > config.attackRange then
        -- Move closer to enemy
        debugLog("Moving closer to enemy (distance: " .. tostring(distance) .. ")")
        moveToVector(target.enemy.position)
        return "moving"
    else
        -- Execute attack rotation
        debugLog("In attack range, executing abilities")
        
        -- Basic attack rotation (customize based on your character class)
        useAbility(0)  -- Primary ability
        
        -- You can add more complex logic here
        if distance <= 3.0 then
            useAbility(2)  -- Close range ability
        else
            useAbility(1)  -- Medium range ability
        end
        
        return "attacking"
    end
end

-- Main combat routine
function advancedCombatRoutine()
    if not isPlayerValid() then
        debugLog("Player not valid, skipping combat routine")
        return false
    end
    
    -- Health check
    if checkAndUsePotion() then
        return true  -- Used potion, skip other actions this frame
    end
    
    -- Find and engage targets
    local target = findBestTarget()
    if target ~= nil then
        state.inCombat = true
        state.currentTarget = target
        
        local action = executeAttackSequence(target)
        debugLog("Combat action: " .. action)
        
        return true
    else
        if state.inCombat then
            log("No more enemies found, exiting combat")
            state.inCombat = false
            state.currentTarget = nil
        end
        return false
    end
end

-- Exploration and farming functions
function farmNearbyItems()
    -- This would be implemented based on your specific needs
    -- Example: collect ground items, interact with objects, etc.
    debugLog("Farming routine not implemented yet")
    return false
end

-- Scene-specific behaviors
function handleSceneLogic()
    local currentScene = getCurrentScene()
    
    if currentScene == "Unknown" then
        return false
    end
    
    debugLog("Current scene: " .. currentScene)
    
    -- Add scene-specific logic here
    if string.find(currentScene, "Monolith") then
        -- Monolith-specific behavior
        return advancedCombatRoutine()
    elseif string.find(currentScene, "Town") then
        -- Town-specific behavior (e.g., vendor interactions)
        return farmNearbyItems()
    else
        -- Default behavior
        return advancedCombatRoutine()
    end
end

-- Main update function (called from C#)
function luaUpdate()
    if not isPlayerValid() then
        return false
    end
    
    local currentTime = os.time()
    
    -- Throttle updates to avoid performance issues
    if currentTime - state.lastCombatCheck < 0.1 then  -- 100ms throttle
        return false
    end
    
    state.lastCombatCheck = currentTime
    
    return handleSceneLogic()
end

-- Event handlers
function onPlayerDamaged(damage)
    log("Player took damage: " .. tostring(damage))
    checkAndUsePotion()
end

function onEnemyKilled(enemyName)
    log("Enemy killed: " .. (enemyName or "Unknown"))
    state.currentTarget = nil
end

function onSceneChanged(sceneName)
    log("Scene changed to: " .. sceneName)
    state.inCombat = false
    state.currentTarget = nil
end

-- Configuration functions
function setHealthThreshold(threshold)
    config.healthThreshold = math.max(0.1, math.min(1.0, threshold))
    log("Health threshold set to: " .. tostring(config.healthThreshold * 100) .. "%")
end

function setCombatRange(range)
    config.combatRange = math.max(5.0, range)
    log("Combat range set to: " .. tostring(config.combatRange))
end

function toggleDebugMode()
    config.debugMode = not config.debugMode
    log("Debug mode: " .. (config.debugMode and "enabled" or "disabled"))
end

-- Test functions for manual execution
function testMovement()
    local playerPos = getPlayerPosition()
    if playerPos ~= nil then
        -- Move in a small circle for testing
        local testPos = createVector3(playerPos.x + 2, playerPos.y, playerPos.z + 2)
        moveToVector(testPos)
        log("Test movement executed")
    end
end

function testAbilities()
    log("Testing abilities...")
    for i = 0, 4 do
        useAbility(i)
        log("Used ability " .. tostring(i))
    end
end

function printPlayerInfo()
    if not isPlayerValid() then
        log("Player not valid")
        return
    end
    
    local pos = getPlayerPosition()
    local health = getPlayerHealth()
    local maxHealth = getPlayerMaxHealth()
    local healthPercent = getPlayerHealthPercent()
    local scene = getCurrentScene()
    local inCombat = isInCombat()
    
    log("=== Player Information ===")
    log("Position: (" .. tostring(pos.x) .. ", " .. tostring(pos.y) .. ", " .. tostring(pos.z) .. ")")
    log("Health: " .. tostring(health) .. "/" .. tostring(maxHealth) .. " (" .. tostring(math.floor(healthPercent * 100)) .. "%)")
    log("Scene: " .. scene)
    log("In Combat: " .. (inCombat and "Yes" or "No"))
    log("========================")
end

-- Initialize the script
function initializeScript()
    log("Advanced Lua script initialized successfully!")
    log("Available functions:")
    log("  - advancedCombatRoutine(): Main combat AI")
    log("  - luaUpdate(): Main update loop")
    log("  - testMovement(): Test movement system")
    log("  - testAbilities(): Test ability usage")
    log("  - printPlayerInfo(): Display player information")
    log("  - setHealthThreshold(value): Set health potion threshold")
    log("  - setCombatRange(value): Set combat detection range")
    log("  - toggleDebugMode(): Toggle debug logging")
    log("")
    log("Hotkeys (when integrated):")
    log("  - F9: Reload Lua scripts")
    log("  - F10: Execute basicCombatRoutine()")
    log("")
    log("Configuration:")
    log("  - Health threshold: " .. tostring(config.healthThreshold * 100) .. "%")
    log("  - Combat range: " .. tostring(config.combatRange))
    log("  - Debug mode: " .. (config.debugMode and "enabled" or "disabled"))
end

-- Call initialization
initializeScript()
