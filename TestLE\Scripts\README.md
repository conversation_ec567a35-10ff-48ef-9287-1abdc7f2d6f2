# TestLE MoonSharp Lua Integration

This directory contains Lua scripts that extend the functionality of the TestLE mod using MoonSharp, a Lua scripting engine for .NET.

## Overview

The MoonSharp integration allows you to write custom game logic in Lua scripts that can interact with the game through a comprehensive C# API. This provides flexibility for creating custom combat routines, automation scripts, and game enhancements without recompiling the mod.

## Getting Started

### 1. Basic Usage

Scripts are automatically loaded from this directory when the mod starts. The main entry point is `init.lua`, which is executed first if it exists.

### 2. Available Scripts

- `init.lua` - Initialization script (auto-loaded)
- `advanced_example.lua` - Comprehensive example showing all features
- `test_integration.lua` - Test suite to verify functionality
- `combat.lua` - Basic combat routine example

### 3. Hotkeys

- **F9**: Reload all Lua scripts
- **F10**: Execute `basicCombatRoutine()` function

## API Reference

### Logging Functions

```lua
log(message)           -- Log an info message
logError(message)      -- Log an error message  
logWarning(message)    -- Log a warning message
```

### Player Functions

```lua
getPlayer()                -- Returns player object (or nil)
getPlayerPosition()        -- Returns Vector3 of player position
getPlayerHealth()          -- Returns current health (float)
getPlayerMaxHealth()       -- Returns maximum health (float)
getPlayerHealthPercent()   -- Returns health as percentage (0.0-1.0)
```

### Movement Functions

```lua
moveTo(x, y, z)           -- Move to specific coordinates
moveToVector(vector3)     -- Move to Vector3 position
```

### Combat Functions

```lua
useAbility(index)                    -- Use ability by index (0-4)
useAbilityAt(index, x, y, z)        -- Use ability at specific location
usePotion()                         -- Use health potion
```

### Utility Functions

```lua
findNearestEnemy(maxDistance)       -- Find nearest enemy within range
getDistance(pos1, pos2)             -- Calculate distance between positions
createVector3(x, y, z)              -- Create a Vector3 object
```

### Game State Functions

```lua
getCurrentScene()                   -- Get current scene name
isInCombat()                       -- Check if player is in combat
```

### Advanced Functions

```lua
getNearbyEnemies(maxDistance)       -- Get all enemies within range
getNearbyGroundItems(maxDistance)   -- Get all ground items within range
```

## Example Scripts

### Basic Combat Routine

```lua
function basicCombatRoutine()
    if not isPlayerValid() then
        return false
    end
    
    -- Check health and use potion if needed
    if getPlayerHealthPercent() < 0.3 then
        usePotion()
        return true
    end
    
    -- Find and attack enemies
    local enemy = findNearestEnemy(30.0)
    if enemy ~= nil then
        local playerPos = getPlayerPosition()
        local distance = getDistance(playerPos, enemy.position)
        
        if distance > 5.0 then
            moveToVector(enemy.position)
        else
            useAbility(0)  -- Basic attack
        end
        return true
    end
    
    return false
end
```

### Health Monitoring

```lua
function monitorHealth()
    local healthPercent = getPlayerHealthPercent()
    
    if healthPercent < 0.2 then
        log("CRITICAL: Health at " .. tostring(healthPercent * 100) .. "%")
        usePotion()
    elseif healthPercent < 0.5 then
        log("WARNING: Health at " .. tostring(healthPercent * 100) .. "%")
    end
end
```

### Scene-Based Logic

```lua
function handleSceneLogic()
    local scene = getCurrentScene()
    
    if string.find(scene, "Monolith") then
        -- Monolith-specific behavior
        return basicCombatRoutine()
    elseif string.find(scene, "Town") then
        -- Town-specific behavior
        log("In town, performing maintenance tasks")
        return false
    else
        -- Default behavior
        return basicCombatRoutine()
    end
end
```

## Best Practices

### 1. Error Handling

Always wrap your code in proper error handling:

```lua
function safeFunction()
    local success, result = pcall(function()
        -- Your code here
        return someOperation()
    end)
    
    if not success then
        logError("Error in safeFunction: " .. tostring(result))
        return false
    end
    
    return result
end
```

### 2. Performance Considerations

- Use throttling for frequently called functions
- Cache expensive calculations
- Avoid creating objects in tight loops

```lua
local lastUpdate = 0
local updateInterval = 0.1  -- 100ms

function throttledUpdate()
    local currentTime = os.time()
    if currentTime - lastUpdate < updateInterval then
        return false
    end
    
    lastUpdate = currentTime
    -- Your update logic here
    return true
end
```

### 3. State Management

Use global tables to maintain state between function calls:

```lua
local state = {
    inCombat = false,
    lastTarget = nil,
    combatStartTime = 0
}

function updateCombatState()
    local enemy = findNearestEnemy(20.0)
    
    if enemy ~= nil and not state.inCombat then
        state.inCombat = true
        state.combatStartTime = os.time()
        log("Entering combat")
    elseif enemy == nil and state.inCombat then
        state.inCombat = false
        log("Exiting combat")
    end
end
```

## Troubleshooting

### Common Issues

1. **Script not loading**: Check file syntax with a Lua validator
2. **Functions not found**: Ensure the function name is spelled correctly
3. **Player functions returning nil**: Player may not be initialized yet
4. **Performance issues**: Add throttling to frequently called functions

### Debugging

Use the test script to verify functionality:

```lua
-- Load and run the test suite
dofile("test_integration.lua")
runAllTests()
```

### Logging

Enable debug logging in your scripts:

```lua
local DEBUG = true

function debugLog(message)
    if DEBUG then
        log("[DEBUG] " .. message)
    end
end
```

## File Organization

Organize your scripts logically:

```
Scripts/
├── init.lua                 -- Main initialization
├── combat/
│   ├── basic_combat.lua     -- Basic combat routines
│   ├── class_specific.lua   -- Class-specific abilities
│   └── targeting.lua        -- Target selection logic
├── utility/
│   ├── movement.lua         -- Movement helpers
│   ├── inventory.lua        -- Inventory management
│   └── helpers.lua          -- General utilities
└── examples/
    ├── advanced_example.lua -- Comprehensive example
    └── test_integration.lua -- Test suite
```

## Contributing

When creating new scripts:

1. Follow the naming conventions
2. Include proper error handling
3. Add logging for important events
4. Test thoroughly before sharing
5. Document your functions

## Support

For issues or questions:

1. Check the test script output
2. Review the mod logs for errors
3. Verify your Lua syntax
4. Test with the provided examples first
