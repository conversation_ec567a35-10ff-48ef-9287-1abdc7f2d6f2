-- TestLE MoonSharp Integration Test Script
-- This script tests all the basic functionality to ensure everything is working

log("Starting MoonSharp integration test...")

-- Test 1: Basic logging
function testLogging()
    log("Test 1: Basic logging - SUCCESS")
    logWarning("Test warning message")
    logError("Test error message (this is intentional)")
    return true
end

-- Test 2: Player information
function testPlayerInfo()
    log("Test 2: Player information")
    
    local player = getPlayer()
    if player == nil then
        log("  Player: Not available (this is normal if not in game)")
        return false
    else
        log("  Player: Available")
    end
    
    local pos = getPlayerPosition()
    if pos ~= nil then
        log("  Position: (" .. tostring(pos.x) .. ", " .. tostring(pos.y) .. ", " .. tostring(pos.z) .. ")")
    else
        log("  Position: Not available")
    end
    
    local health = getPlayerHealth()
    local maxHealth = getPlayerMaxHealth()
    local healthPercent = getPlayerHealthPercent()
    
    log("  Health: " .. tostring(health) .. "/" .. tostring(maxHealth) .. " (" .. tostring(math.floor(healthPercent * 100)) .. "%)")
    
    return true
end

-- Test 3: Vector operations
function testVectorOperations()
    log("Test 3: Vector operations")
    
    local vec1 = createVector3(1.0, 2.0, 3.0)
    local vec2 = createVector3(4.0, 5.0, 6.0)
    
    if vec1 ~= nil and vec2 ~= nil then
        local distance = getDistance(vec1, vec2)
        log("  Distance between (1,2,3) and (4,5,6): " .. tostring(distance))
        log("  Expected: ~5.196, Actual: " .. tostring(distance))
        
        if math.abs(distance - 5.196) < 0.01 then
            log("  Vector operations: SUCCESS")
            return true
        else
            log("  Vector operations: FAILED - Distance calculation incorrect")
            return false
        end
    else
        log("  Vector operations: FAILED - Could not create vectors")
        return false
    end
end

-- Test 4: Game state functions
function testGameState()
    log("Test 4: Game state functions")
    
    local scene = getCurrentScene()
    log("  Current scene: " .. scene)
    
    local inCombat = isInCombat()
    log("  In combat: " .. (inCombat and "Yes" or "No"))
    
    return true
end

-- Test 5: Enemy detection
function testEnemyDetection()
    log("Test 5: Enemy detection")
    
    local enemy = findNearestEnemy(50.0)
    if enemy ~= nil then
        log("  Found enemy at position: (" .. tostring(enemy.position.x) .. ", " .. tostring(enemy.position.y) .. ", " .. tostring(enemy.position.z) .. ")")
        log("  Enemy is active: " .. (enemy.isActive and "Yes" or "No"))
        return true
    else
        log("  No enemies found within 50 units (this is normal if not in combat area)")
        return true
    end
end

-- Test 6: Safe ability usage (won't actually use abilities unless player is valid)
function testAbilitySystem()
    log("Test 6: Ability system")
    
    if not isPlayerValid() then
        log("  Player not valid, skipping ability test")
        return true
    end
    
    log("  Player is valid, ability system ready")
    log("  Note: Not actually using abilities in test mode")
    
    -- In a real scenario, you would call:
    -- useAbility(0)
    -- usePotion()
    -- etc.
    
    return true
end

-- Test 7: Movement system
function testMovementSystem()
    log("Test 7: Movement system")
    
    if not isPlayerValid() then
        log("  Player not valid, skipping movement test")
        return true
    end
    
    log("  Player is valid, movement system ready")
    log("  Note: Not actually moving in test mode")
    
    -- In a real scenario, you would call:
    -- moveTo(100, 0, 100)
    -- or moveToVector(someVector)
    
    return true
end

-- Main test runner
function runAllTests()
    log("=== MoonSharp Integration Test Suite ===")
    
    local tests = {
        {"Logging", testLogging},
        {"Player Info", testPlayerInfo},
        {"Vector Operations", testVectorOperations},
        {"Game State", testGameState},
        {"Enemy Detection", testEnemyDetection},
        {"Ability System", testAbilitySystem},
        {"Movement System", testMovementSystem}
    }
    
    local passed = 0
    local total = #tests
    
    for i, test in ipairs(tests) do
        local name = test[1]
        local func = test[2]
        
        log("")
        log("Running test: " .. name)
        
        local success, result = pcall(func)
        if success and result then
            log("✓ " .. name .. " - PASSED")
            passed = passed + 1
        else
            log("✗ " .. name .. " - FAILED")
            if not success then
                log("  Error: " .. tostring(result))
            end
        end
    end
    
    log("")
    log("=== Test Results ===")
    log("Passed: " .. tostring(passed) .. "/" .. tostring(total))
    
    if passed == total then
        log("🎉 All tests passed! MoonSharp integration is working correctly.")
    else
        log("⚠️  Some tests failed. Check the logs above for details.")
    end
    
    return passed == total
end

-- Helper function to check if player is valid
function isPlayerValid()
    local player = getPlayer()
    return player ~= nil
end

-- Quick test function for manual execution
function quickTest()
    log("=== Quick Test ===")
    testLogging()
    testVectorOperations()
    testGameState()
    log("Quick test completed!")
end

-- Run tests automatically when script loads
log("MoonSharp integration test script loaded successfully!")
log("Available test functions:")
log("  - runAllTests(): Run complete test suite")
log("  - quickTest(): Run basic functionality tests")
log("  - Individual test functions: testLogging(), testPlayerInfo(), etc.")
log("")
log("To run tests, call: runAllTests()")
