using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Il2Cpp;
using MelonLoader;
using MoonSharp.Interpreter;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE;

/// <summary>
/// Manages Lua script execution using MoonSharp for the TestLE mod.
/// Provides a bridge between C# game functionality and Lua scripting.
/// </summary>
public partial class LuaScriptManager
{
    private readonly Dictionary<string, Script> _loadedScripts = new();
    private readonly Dictionary<string, DynValue> _scriptResults = new();
    private Script? _globalScript;
    
    public bool IsInitialized { get; private set; }
    
    /// <summary>
    /// Initializes the Lua script manager and sets up the global script environment.
    /// </summary>
    public void Initialize()
    {
        try
        {
            // Create global script instance
            _globalScript = new Script();
            
            // Register C# types and functions for Lua access
            RegisterCSharpAPI();
            
            // Load and execute initialization scripts
            LoadInitializationScripts();
            
            IsInitialized = true;
            MelonLogger.Msg("LuaScriptManager initialized successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize LuaScriptManager: {ex}");
            IsInitialized = false;
        }
    }
    
    /// <summary>
    /// Registers C# types, functions, and objects for access from Lua scripts.
    /// </summary>
    private void RegisterCSharpAPI()
    {
        if (_globalScript == null) return;
        
        // Register Unity types
        UserData.RegisterType<Vector3>();
        UserData.RegisterType<Vector2>();
        UserData.RegisterType<Transform>();
        UserData.RegisterType<GameObject>();
        
        // Register game-specific types
        UserData.RegisterType<LocalPlayer>();
        
        // Register utility classes
        UserData.RegisterAssembly();
        
        // Create global Lua functions
        _globalScript.Globals["log"] = (Action<string>)LogMessage;
        _globalScript.Globals["logError"] = (Action<string>)LogError;
        _globalScript.Globals["logWarning"] = (Action<string>)LogWarning;
        
        // Player functions
        _globalScript.Globals["getPlayer"] = (Func<DynValue>)GetPlayer;
        _globalScript.Globals["getPlayerPosition"] = (Func<DynValue>)GetPlayerPosition;
        _globalScript.Globals["getPlayerHealth"] = (Func<float>)GetPlayerHealth;
        _globalScript.Globals["getPlayerMaxHealth"] = (Func<float>)GetPlayerMaxHealth;
        _globalScript.Globals["getPlayerHealthPercent"] = (Func<float>)GetPlayerHealthPercent;
        
        // Movement functions
        _globalScript.Globals["moveTo"] = (Action<float, float, float>)MoveTo;
        _globalScript.Globals["moveToVector"] = (Action<DynValue>)MoveToVector;
        
        // Combat functions
        _globalScript.Globals["useAbility"] = (Action<int>)UseAbility;
        _globalScript.Globals["useAbilityAt"] = (Action<int, float, float, float>)UseAbilityAt;
        _globalScript.Globals["usePotion"] = (Action)UsePotion;
        
        // Utility functions
        _globalScript.Globals["findNearestEnemy"] = (Func<float, DynValue>)FindNearestEnemy;
        _globalScript.Globals["getDistance"] = (Func<DynValue, DynValue, float>)GetDistance;
        _globalScript.Globals["createVector3"] = (Func<float, float, float, DynValue>)CreateVector3;
        
        // Game state functions
        _globalScript.Globals["getCurrentScene"] = (Func<string>)GetCurrentScene;
        _globalScript.Globals["isInCombat"] = (Func<bool>)IsInCombat;

        // Advanced functions
        _globalScript.Globals["getNearbyEnemies"] = (Func<float, DynValue>)GetNearbyEnemies;
        _globalScript.Globals["getNearbyGroundItems"] = (Func<float, DynValue>)GetNearbyGroundItems;
        
        MelonLogger.Msg("C# API registered for Lua scripts");
    }
    
    /// <summary>
    /// Loads initialization scripts from the Scripts directory.
    /// </summary>
    private void LoadInitializationScripts()
    {
        var scriptsPath = Path.Combine(Application.persistentDataPath, "TestLE", "Scripts");
        
        if (!Directory.Exists(scriptsPath))
        {
            Directory.CreateDirectory(scriptsPath);
            MelonLogger.Msg($"Created scripts directory: {scriptsPath}");
            
            // Create example scripts
            CreateExampleScripts(scriptsPath);
        }
        
        // Load init.lua if it exists
        var initScript = Path.Combine(scriptsPath, "init.lua");
        if (File.Exists(initScript))
        {
            ExecuteScriptFile(initScript);
        }
    }
    
    /// <summary>
    /// Creates example Lua scripts to demonstrate functionality.
    /// </summary>
    private void CreateExampleScripts(string scriptsPath)
    {
        // Create init.lua
        var initLua = @"-- TestLE Lua Initialization Script
log('Lua scripting system initialized!')

-- Global variables
playerCheckInterval = 1.0
lastPlayerCheck = 0

-- Helper functions
function isPlayerValid()
    local player = getPlayer()
    return player ~= nil
end

function checkPlayerHealth()
    if not isPlayerValid() then
        return false
    end
    
    local healthPercent = getPlayerHealthPercent()
    if healthPercent < 0.3 then
        log('Player health low: ' .. tostring(healthPercent * 100) .. '%')
        usePotion()
        return true
    end
    return false
end

log('Init script loaded successfully')
";
        
        File.WriteAllText(Path.Combine(scriptsPath, "init.lua"), initLua);
        
        // Create combat.lua example
        var combatLua = @"-- TestLE Combat Script Example
function basicCombatRoutine()
    if not isPlayerValid() then
        return false
    end
    
    local enemy = findNearestEnemy(30.0)
    if enemy == nil then
        return false
    end
    
    local playerPos = getPlayerPosition()
    local distance = getDistance(playerPos, enemy.position)
    
    if distance > 5.0 then
        -- Move closer to enemy
        moveToVector(enemy.position)
        log('Moving to enemy at distance: ' .. tostring(distance))
    else
        -- Use abilities
        useAbility(0) -- Basic attack
        log('Attacking enemy')
    end
    
    return true
end

log('Combat script loaded')
";
        
        File.WriteAllText(Path.Combine(scriptsPath, "combat.lua"), combatLua);
        
        MelonLogger.Msg("Created example Lua scripts");
    }
    
    /// <summary>
    /// Executes a Lua script from a file.
    /// </summary>
    public DynValue? ExecuteScriptFile(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                MelonLogger.Error($"Script file not found: {filePath}");
                return null;
            }
            
            var scriptContent = File.ReadAllText(filePath);
            return ExecuteScript(scriptContent, Path.GetFileName(filePath));
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing script file {filePath}: {ex}");
            return null;
        }
    }
    
    /// <summary>
    /// Executes a Lua script from a string.
    /// </summary>
    public DynValue? ExecuteScript(string scriptContent, string scriptName = "anonymous")
    {
        try
        {
            if (_globalScript == null)
            {
                MelonLogger.Error("LuaScriptManager not initialized");
                return null;
            }
            
            var result = _globalScript.DoString(scriptContent);
            _scriptResults[scriptName] = result;
            
            MelonLogger.Msg($"Executed Lua script: {scriptName}");
            return result;
        }
        catch (ScriptRuntimeException ex)
        {
            MelonLogger.Error($"Lua runtime error in {scriptName}: {ex.DecoratedMessage}");
            return null;
        }
        catch (SyntaxErrorException ex)
        {
            MelonLogger.Error($"Lua syntax error in {scriptName}: {ex.DecoratedMessage}");
            return null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error executing Lua script {scriptName}: {ex}");
            return null;
        }
    }
    
    /// <summary>
    /// Calls a Lua function with optional parameters.
    /// </summary>
    public DynValue? CallLuaFunction(string functionName, params object[] args)
    {
        try
        {
            if (_globalScript == null)
            {
                MelonLogger.Error("LuaScriptManager not initialized");
                return null;
            }
            
            var function = _globalScript.Globals[functionName];
            if (function == null || (function is DynValue dynValue && dynValue.Type != DataType.Function))
            {
                MelonLogger.Warning($"Lua function '{functionName}' not found or not a function");
                return null;
            }
            
            return _globalScript.Call(function, args);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error calling Lua function {functionName}: {ex}");
            return null;
        }
    }
    
    /// <summary>
    /// Gets a Lua global variable value.
    /// </summary>
    public DynValue? GetLuaGlobal(string variableName)
    {
        try
        {
            var value = _globalScript?.Globals[variableName];
            return value as DynValue;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting Lua global {variableName}: {ex}");
            return null;
        }
    }
    
    /// <summary>
    /// Sets a Lua global variable value.
    /// </summary>
    public void SetLuaGlobal(string variableName, object value)
    {
        try
        {
            if (_globalScript != null)
            {
                _globalScript.Globals[variableName] = DynValue.FromObject(_globalScript, value);
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error setting Lua global {variableName}: {ex}");
        }
    }
    
    /// <summary>
    /// Reloads all scripts from the Scripts directory.
    /// </summary>
    public void ReloadScripts()
    {
        try
        {
            _loadedScripts.Clear();
            _scriptResults.Clear();
            
            if (_globalScript != null)
            {
                _globalScript.Globals.Clear();
                RegisterCSharpAPI();
                LoadInitializationScripts();
            }
            
            MelonLogger.Msg("Lua scripts reloaded");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error reloading scripts: {ex}");
        }
    }
    
    /// <summary>
    /// Cleans up resources.
    /// </summary>
    public void Cleanup()
    {
        _loadedScripts.Clear();
        _scriptResults.Clear();
        _globalScript = null;
        IsInitialized = false;
        MelonLogger.Msg("LuaScriptManager cleaned up");
    }
}
