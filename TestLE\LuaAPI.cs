using System;
using System.Linq;
using MelonLoader;
using MoonSharp.Interpreter;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE;

/// <summary>
/// Contains API functions that can be called from Lua scripts.
/// This partial class extends LuaScriptManager with the actual API implementations.
/// </summary>
public partial class LuaScriptManager
{
    #region Logging Functions
    
    private void LogMessage(string message)
    {
        MelonLogger.Msg($"[Lua] {message}");
    }
    
    private void LogError(string message)
    {
        MelonLogger.Error($"[Lua] {message}");
    }
    
    private void LogWarning(string message)
    {
        MelonLogger.Warning($"[Lua] {message}");
    }
    
    #endregion
    
    #region Player Functions
    
    private DynValue GetPlayer()
    {
        try
        {
            var player = Globals.PLAYER;
            return player != null ? DynValue.FromObject(_globalScript, player) : DynValue.Nil;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player: {ex}");
            return DynValue.Nil;
        }
    }
    
    private DynValue GetPlayerPosition()
    {
        try
        {
            var player = Globals.PLAYER;
            if (player?.transform == null) return DynValue.Nil;
            
            var pos = player.transform.position;
            return DynValue.FromObject(_globalScript, pos);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player position: {ex}");
            return DynValue.Nil;
        }
    }
    
    private float GetPlayerHealth()
    {
        try
        {
            var player = Globals.PLAYER;
            return player?.playerHealth?.currentHealth ?? 0f;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player health: {ex}");
            return 0f;
        }
    }
    
    private float GetPlayerMaxHealth()
    {
        try
        {
            var player = Globals.PLAYER;
            return player?.playerHealth?.maxHealth ?? 0f;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player max health: {ex}");
            return 0f;
        }
    }
    
    private float GetPlayerHealthPercent()
    {
        try
        {
            var player = Globals.PLAYER;
            if (player?.playerHealth == null) return 0f;
            
            var current = player.playerHealth.currentHealth;
            var max = player.playerHealth.maxHealth;
            
            return max > 0 ? current / max : 0f;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting player health percent: {ex}");
            return 0f;
        }
    }
    
    #endregion
    
    #region Movement Functions
    
    private void MoveTo(float x, float y, float z)
    {
        try
        {
            var position = new Vector3(x, y, z);
            PlayerHelpers.MoveTo(position);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error moving to position ({x}, {y}, {z}): {ex}");
        }
    }
    
    private void MoveToVector(DynValue vectorValue)
    {
        try
        {
            if (vectorValue.Type != DataType.UserData) return;
            
            var vector = vectorValue.UserData.Object as Vector3?;
            if (vector.HasValue)
            {
                PlayerHelpers.MoveTo(vector.Value);
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error moving to vector: {ex}");
        }
    }
    
    #endregion
    
    #region Combat Functions
    
    private void UseAbility(int abilityIndex)
    {
        try
        {
            var player = Globals.PLAYER;
            if (player == null) return;
            
            // Find nearest enemy for targeting
            var (enemy, _) = FindHelpers.FindNearestEnemy(player.transform.position, 30f);
            if (enemy?.Data?.transform != null)
            {
                PlayerHelpers.UseAbility(abilityIndex, enemy.Data.transform);
            }
            else
            {
                // Use ability without specific target
                PlayerHelpers.UseAbility(abilityIndex, null!);
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error using ability {abilityIndex}: {ex}");
        }
    }
    
    private void UseAbilityAt(int abilityIndex, float x, float y, float z)
    {
        try
        {
            var targetPosition = new Vector3(x, y, z);
            
            // Create a temporary transform for targeting
            var tempObject = new GameObject("LuaAbilityTarget");
            tempObject.transform.position = targetPosition;
            
            PlayerHelpers.UseAbility(abilityIndex, tempObject.transform);
            
            // Clean up the temporary object
            UnityEngine.Object.Destroy(tempObject);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error using ability {abilityIndex} at ({x}, {y}, {z}): {ex}");
        }
    }
    
    private void UsePotion()
    {
        try
        {
            PlayerHelpers.UsePotion();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error using potion: {ex}");
        }
    }
    
    #endregion
    
    #region Utility Functions
    
    private DynValue FindNearestEnemy(float maxDistance)
    {
        try
        {
            var player = Globals.PLAYER;
            if (player?.transform == null) return DynValue.Nil;
            
            var (enemy, distance) = FindHelpers.FindNearestEnemy(player.transform.position, maxDistance);
            if (enemy?.Data != null)
            {
                // Create a Lua-friendly object with enemy information
                var enemyInfo = new Table(_globalScript);
                enemyInfo["position"] = DynValue.FromObject(_globalScript, enemy.Data.transform.position);
                enemyInfo["gameObject"] = DynValue.FromObject(_globalScript, enemy.Data.gameObject);
                enemyInfo["transform"] = DynValue.FromObject(_globalScript, enemy.Data.transform);
                enemyInfo["isActive"] = DynValue.FromObject(_globalScript, enemy.Data.isActiveAndEnabled);
                enemyInfo["distance"] = distance;

                return DynValue.NewTable(enemyInfo);
            }
            
            return DynValue.Nil;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error finding nearest enemy: {ex}");
            return DynValue.Nil;
        }
    }
    
    private float GetDistance(DynValue pos1, DynValue pos2)
    {
        try
        {
            if (pos1.Type != DataType.UserData || pos2.Type != DataType.UserData)
                return float.MaxValue;
            
            var vector1 = pos1.UserData.Object as Vector3?;
            var vector2 = pos2.UserData.Object as Vector3?;
            
            if (vector1.HasValue && vector2.HasValue)
            {
                return Vector3.Distance(vector1.Value, vector2.Value);
            }
            
            return float.MaxValue;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error calculating distance: {ex}");
            return float.MaxValue;
        }
    }
    
    private DynValue CreateVector3(float x, float y, float z)
    {
        try
        {
            var vector = new Vector3(x, y, z);
            return DynValue.FromObject(_globalScript, vector);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error creating Vector3: {ex}");
            return DynValue.Nil;
        }
    }
    
    #endregion
    
    #region Game State Functions
    
    private string GetCurrentScene()
    {
        return Globals.CURRENT_SCENE ?? "Unknown";
    }
    
    private bool IsInCombat()
    {
        try
        {
            // Check if there are any nearby enemies
            var player = Globals.PLAYER;
            if (player?.transform == null) return false;
            
            var (enemy, _) = FindHelpers.FindNearestEnemy(player.transform.position, 15f);
            return enemy != null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking combat state: {ex}");
            return false;
        }
    }
    
    #endregion
    
    #region Advanced Functions
    
    /// <summary>
    /// Gets information about all enemies within a specified range.
    /// </summary>
    private DynValue GetNearbyEnemies(float maxDistance)
    {
        try
        {
            var player = Globals.PLAYER;
            if (player?.transform == null) return DynValue.Nil;
            
            var enemies = Globals.ENEMIES
                .Where(e => e?.Data != null && e.Data.isActiveAndEnabled)
                .Where(e => Vector3.Distance(player.transform.position, e.Data.transform.position) <= maxDistance)
                .ToList();

            var enemyTable = new Table(_globalScript);
            for (int i = 0; i < enemies.Count; i++)
            {
                var enemy = enemies[i];
                if (enemy?.Data == null) continue;

                var enemyInfo = new Table(_globalScript);
                enemyInfo["position"] = DynValue.FromObject(_globalScript, enemy.Data.transform.position);
                enemyInfo["distance"] = Vector3.Distance(player.transform.position, enemy.Data.transform.position);
                enemyInfo["gameObject"] = DynValue.FromObject(_globalScript, enemy.Data.gameObject);

                enemyTable[i + 1] = DynValue.NewTable(enemyInfo); // Lua arrays are 1-indexed
            }
            
            return DynValue.NewTable(enemyTable);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting nearby enemies: {ex}");
            return DynValue.Nil;
        }
    }
    
    /// <summary>
    /// Gets information about ground items within a specified range.
    /// </summary>
    private DynValue GetNearbyGroundItems(float maxDistance)
    {
        try
        {
            var player = Globals.PLAYER;
            if (player?.transform == null) return DynValue.Nil;
            
            var items = Globals.GROUND_ITEMS
                .Where(item => item?.Label?.transform != null)
                .Where(item => Vector3.Distance(player.transform.position, item.Label.transform.position) <= maxDistance)
                .ToList();

            var itemTable = new Table(_globalScript);
            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                if (item?.Label == null) continue;

                var itemInfo = new Table(_globalScript);
                itemInfo["position"] = DynValue.FromObject(_globalScript, item.Label.transform.position);
                itemInfo["distance"] = Vector3.Distance(player.transform.position, item.Label.transform.position);
                itemInfo["name"] = item.Label.name ?? "Unknown";

                itemTable[i + 1] = DynValue.NewTable(itemInfo);
            }
            
            return DynValue.NewTable(itemTable);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error getting nearby ground items: {ex}");
            return DynValue.Nil;
        }
    }
    
    #endregion
}
